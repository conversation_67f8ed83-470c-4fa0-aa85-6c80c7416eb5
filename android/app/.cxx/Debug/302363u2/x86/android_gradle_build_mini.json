{"buildFiles": ["/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/user-jaagur/android/app/.cxx/Debug/302363u2/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/user-jaagur/android/app/.cxx/Debug/302363u2/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}