{"buildFiles": ["/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/projects/sixvalley-user-app/android/app/.cxx/Debug/202s695w/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/projects/sixvalley-user-app/android/app/.cxx/Debug/202s695w/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}