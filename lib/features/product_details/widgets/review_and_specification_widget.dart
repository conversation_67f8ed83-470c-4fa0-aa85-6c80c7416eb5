import 'package:flutter/material.dart';
import 'package:flutter_sixvalley_ecommerce/features/product_details/controllers/product_details_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/review/controllers/review_controller.dart';
import 'package:flutter_sixvalley_ecommerce/localization/language_constrants.dart';
import 'package:flutter_sixvalley_ecommerce/theme/controllers/theme_controller.dart';
import 'package:flutter_sixvalley_ecommerce/utill/custom_themes.dart';
import 'package:flutter_sixvalley_ecommerce/utill/dimensions.dart';
import 'package:provider/provider.dart';

class ReviewAndSpecificationSectionWidget extends StatelessWidget {
  const ReviewAndSpecificationSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Review section completely hidden - only show specification tab
    return Consumer<ProductDetailsController>(
        builder: (context, productDetailsController, _) {
      return Padding(
        padding:
            const EdgeInsets.symmetric(vertical: Dimensions.paddingSizeDefault),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Only show Specification tab, Reviews tab hidden
            Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: Dimensions.paddingSizeDefault,
                  vertical: Dimensions.paddingSizeSmall),
              decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(Dimensions.paddingSizeExtraSmall),
                  color: Provider.of<ThemeController>(context, listen: false)
                          .darkTheme
                      ? Theme.of(context).hintColor.withValues(alpha: .25)
                      : Theme.of(context).primaryColor.withValues(alpha: .05)),
              child: Text(
                '${getTranslated('specification', context)}',
                style: textMedium.copyWith(
                    fontSize: Dimensions.fontSizeDefault,
                    color: Provider.of<ThemeController>(context, listen: false)
                            .darkTheme
                        ? Theme.of(context).hintColor
                        : Theme.of(context).primaryColor),
              ),
            ),
            const SizedBox(height: Dimensions.paddingSizeExtraSmall),
            // Active indicator for specification tab - positioned below the text
            Container(
              width: 60,
              height: 3,
              color: Theme.of(context).primaryColor,
            ),

            // Reviews tab completely hidden as per requirements
            // const SizedBox(width: Dimensions.paddingSizeDefault),
            // InkWell(
            //   onTap: () => productDetailsController.selectReviewSection(true),
            //   child: Stack(
            //     clipBehavior: Clip.none,
            //     children: [
            //       Column(children: [
            //         Container(
            //           padding: const EdgeInsets.symmetric(
            //               horizontal: Dimensions.paddingSizeDefault,
            //               vertical: Dimensions.paddingSizeSmall),
            //           decoration: BoxDecoration(
            //               borderRadius: BorderRadius.circular(
            //                   Dimensions.paddingSizeExtraSmall),
            //               color: productDetailsController.isReviewSelected
            //                   ? Provider.of<ThemeController>(context,
            //                               listen: false)
            //                           .darkTheme
            //                       ? Theme.of(context)
            //                           .hintColor
            //                           .withValues(alpha: .25)
            //                       : Theme.of(context)
            //                           .primaryColor
            //                           .withValues(alpha: .05)
            //                   : Colors.transparent),
            //           child: Text(
            //             '${getTranslated('reviews', context)}',
            //             style: textRegular.copyWith(
            //                 color: Provider.of<ThemeController>(context,
            //                             listen: false)
            //                         .darkTheme
            //                     ? Theme.of(context).hintColor
            //                     : Theme.of(context).primaryColor),
            //           ),
            //         ),
            //         if (productDetailsController.isReviewSelected)
            //           Container(
            //               width: 60,
            //               height: 3,
            //               color: Theme.of(context).primaryColor)
            //       ]),
            //     ],
            //   ),
            // )
          ],
        ),
      );
    });
  }
}
