import 'package:flutter/material.dart';

class RatingBar extends StatelessWidget {
  final double rating;
  final double size;

  const RatingBar({super.key, required this.rating, this.size = 18});

  @override
  Widget build(BuildContext context) {
    // Rating bar completely hidden as per requirements
    return const SizedBox.shrink();

    // Original implementation commented out
    // List<Widget> starList = [];
    //
    // int realNumber = rating.floor();
    // int partNumber = ((rating - realNumber) * 10).ceil();
    //
    // for (int i = 1; i <= 5; i++) {
    //   if (i < realNumber) {
    //     starList.add(Icon(Icons.star, color: Colors.orange, size: size));
    //   } else if (i == realNumber) {
    //     starList.add(SizedBox(
    //       height: size,
    //       width: size,
    //       child: Stack(
    //         fit: StackFit.expand,
    //         children: [
    //           Icon(Icons.star, color: Colors.orange, size: size),
    //           ClipRect(
    //             clipper: _Clipper(part: partNumber),
    //             child: Icon(Icons.star_border, color: Colors.orange, size: size),
    //           )
    //         ],
    //       ),
    //     ));
    //   } else {
    //     starList.add(Icon(Icons.star_border, color: Colors.orange, size: size));
    //   }
    // }
    //
    // return Row(
    //   mainAxisSize: MainAxisSize.min,
    //   children: starList,
    // );
  }
}

class _Clipper extends CustomClipper<Rect> {
  final int part;

  _Clipper({required this.part});

  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(
      (size.width / 10) * part,
      0.0,
      size.width,
      size.height,
    );
  }

  @override
  bool shouldReclip(CustomClipper<Rect> oldClipper) => true;
}
