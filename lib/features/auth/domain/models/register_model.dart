class RegisterModel {
  String? email;
  String? password;
  String? fName;
  String? lName;
  String? phone;
  String? socialId;
  String? loginMedium;
  String? referCode;
  String? gstin;
  String? shopName;
  String? address;
  String? city;
  String? zip;
  String? country;
  String? latitude;
  String? longitude;

  RegisterModel({this.email, this.password, this.fName, this.lName, this.phone, this.socialId,this.loginMedium, this.referCode, this.gstin, this.shopName, this.address, this.city, this.zip, this.country, this.latitude, this.longitude});

  RegisterModel.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    password = json['password'];
    fName = json['f_name'];
    lName = json['l_name'];
    phone = json['phone'];
    socialId = json['social_id'];
    loginMedium = json['login_medium'];
    referCode = json['referral_code'];
    gstin = json['gstin'];
    shopName = json['shop_name'];
    address = json['address'];
    city = json['city'];
    zip = json['zip'];
    country = json['country'];
    latitude = json['latitude'];
    longitude = json['longitude'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email;
    data['password'] = password;
    data['f_name'] = fName;
    data['l_name'] = lName;
    data['phone'] = phone;
    data['social_id'] = socialId;
    data['login_medium'] = loginMedium;
    data['referral_code'] = referCode;
    data['gstin'] = gstin;
    data['shop_name'] = shopName;
    data['address'] = address;
    data['city'] = city;
    data['zip'] = zip;
    data['country'] = country;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    return data;
  }
}
