import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sixvalley_ecommerce/features/product/controllers/product_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/product/screens/brand_and_category_product_screen.dart';
import 'package:flutter_sixvalley_ecommerce/features/category/controllers/category_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/category/domain/models/category_model.dart';
import 'package:flutter_sixvalley_ecommerce/features/brand/controllers/brand_controller.dart';
import 'package:flutter_sixvalley_ecommerce/helper/responsive_helper.dart';
import 'package:flutter_sixvalley_ecommerce/localization/language_constrants.dart';
import 'package:flutter_sixvalley_ecommerce/localization/controllers/localization_controller.dart';
import 'package:flutter_sixvalley_ecommerce/theme/controllers/theme_controller.dart';
import 'package:flutter_sixvalley_ecommerce/utill/custom_themes.dart';
import 'package:flutter_sixvalley_ecommerce/utill/dimensions.dart';
import 'package:flutter_sixvalley_ecommerce/common/basewidget/custom_image_widget.dart';
import 'package:flutter_sixvalley_ecommerce/features/home/<USER>/aster_theme/find_what_you_need_shimmer.dart';
import 'package:provider/provider.dart';

class FindWhatYouNeedView extends StatelessWidget {
  const FindWhatYouNeedView({super.key});

  /// Helper method to find CategoryModel by ID from the category list
  CategoryModel? _findCategoryById(
      int? categoryId, List<CategoryModel> categoryList) {
    if (categoryId == null) return null;

    try {
      return categoryList.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      // Category not found
      return null;
    }
  }

  // /// Helper method to navigate to brand products
  // void _navigateToBrandProducts(
  //     BuildContext context, int? brandId, String? brandName) {
  //   if (brandId == null) return;
  //
  //   print('FindWhatYouNeed: Brand tap - $brandName (ID: $brandId)');
  //
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(
  //       builder: (_) => BrandAndCategoryProductScreen(
  //         isBrand: true,
  //         id: brandId,
  //         name: brandName,
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Consumer3<ProductController, CategoryController, BrandController>(
        builder: (context, productController, categoryController,
            brandController, _) {
      return productController.findWhatYouNeedModel != null
          ? (productController.findWhatYouNeedModel!.findWhatYouNeed != null &&
                  productController
                      .findWhatYouNeedModel!.findWhatYouNeed!.isNotEmpty)
              ? SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: 120,
                  child: CarouselSlider.builder(
                    options: CarouselOptions(
                      padEnds: false,
                      viewportFraction: .80,
                      autoPlay: true,
                      enableInfiniteScroll: false,
                      onPageChanged: (index, reason) {},
                    ),
                    itemCount: productController
                        .findWhatYouNeedModel!.findWhatYouNeed!.length,
                    itemBuilder: (context, index, _) {
                      return InkWell(
                        onTap: () {
                          final categoryId = productController
                              .findWhatYouNeedModel!.findWhatYouNeed![index].id;
                          final categoryName = productController
                              .findWhatYouNeedModel!
                              .findWhatYouNeed![index]
                              .name;

                          // Debug: Print navigation details
                          print(
                              'FindWhatYouNeed: Main category tap - $categoryName (ID: $categoryId)');

                          // Find the corresponding CategoryModel to enable sub-category navigation
                          final categoryModel = _findCategoryById(
                              categoryId, categoryController.categoryList);

                          // Debug: Print if CategoryModel was found
                          print(
                              'FindWhatYouNeed: CategoryModel found: ${categoryModel != null ? categoryModel.name : 'null'}');

                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (_) => BrandAndCategoryProductScreen(
                                        isBrand: false,
                                        id: categoryId,
                                        name: categoryName,
                                        categoryModel: categoryModel,
                                      )));
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: Provider.of<LocalizationController>(context,
                                          listen: false)
                                      .isLtr
                                  ? Dimensions.paddingSizeDefault
                                  : 0,
                              right: index + 1 ==
                                      productController.findWhatYouNeedModel!
                                          .findWhatYouNeed!.length
                                  ? Dimensions.paddingSizeDefault
                                  : Provider.of<LocalizationController>(context,
                                              listen: false)
                                          .isLtr
                                      ? 0
                                      : Dimensions.paddingSizeDefault),
                          child: Container(
                            width: 305,
                            height: 140,
                            decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.125),
                                shape: BoxShape.rectangle,
                                borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(
                                        Dimensions.radiusDefault),
                                    bottomLeft: Radius.circular(
                                        Dimensions.radiusDefault),
                                    bottomRight: Radius.circular(
                                        Dimensions.radiusDefault))),
                            child: Stack(
                              children: [
                                // Positioned(top: 0, right: 0,
                                //   child: Container(height: 23, width: 24,
                                //     decoration: BoxDecoration(
                                //       borderRadius: const BorderRadius.only(
                                //         bottomLeft: Radius.circular(Dimensions.radiusSmall)),
                                //       gradient: LinearGradient(stops: const [.5, .5], begin: Alignment.bottomLeft, end: Alignment.topRight,
                                //         colors: [Theme.of(context).primaryColor, Provider.of<ThemeController>(context).darkTheme ?
                                //         Colors.black : Colors.white],
                                //       ),
                                //     ),
                                //   ),
                                // ),

                                Padding(
                                  padding: const EdgeInsets.all(
                                      Dimensions.paddingSizeSmall),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          '${productController.findWhatYouNeedModel!.findWhatYouNeed![index].name}',
                                          style: textRegular.copyWith(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              fontSize:
                                                  Dimensions.fontSizeLarge,
                                              fontWeight: FontWeight.w600)),
                                      const SizedBox(
                                          height:
                                              Dimensions.paddingSizeExtraSmall),
                                      Text(
                                          '${productController.findWhatYouNeedModel!.findWhatYouNeed![index].productCount} ${getTranslated('products', context)}',
                                          style: textRegular.copyWith(
                                              color:
                                                  Theme.of(context).hintColor)),
                                      const SizedBox(
                                          height: Dimensions.paddingSizeSmall),
                                      Row(
                                        children: [
                                          SizedBox(
                                            height: 65,
                                            width: 270,
                                            child: ListView.builder(
                                              itemCount: productController
                                                  .findWhatYouNeedModel!
                                                  .findWhatYouNeed![index]
                                                  .childes!
                                                  .length,
                                              scrollDirection: Axis.horizontal,
                                              itemBuilder: (context, subIndex) {
                                                return InkWell(
                                                  onTap: () {
                                                    final subCategoryId =
                                                        productController
                                                            .findWhatYouNeedModel
                                                            ?.findWhatYouNeed![
                                                                index]
                                                            .childes?[subIndex]
                                                            .id;
                                                    final subCategoryName =
                                                        productController
                                                            .findWhatYouNeedModel
                                                            ?.findWhatYouNeed![
                                                                index]
                                                            .childes?[subIndex]
                                                            .name;
                                                    final parentCategoryId =
                                                        productController
                                                            .findWhatYouNeedModel!
                                                            .findWhatYouNeed![
                                                                index]
                                                            .id;
                                                    final parentCategoryName =
                                                        productController
                                                            .findWhatYouNeedModel!
                                                            .findWhatYouNeed![
                                                                index]
                                                            .name;

                                                    // Debug: Print navigation details
                                                    print(
                                                        'FindWhatYouNeed: Sub-category tap - Parent: $parentCategoryName (ID: $parentCategoryId), Sub: $subCategoryName (ID: $subCategoryId)');

                                                    // Find the corresponding CategoryModel for better navigation
                                                    final categoryModel =
                                                        _findCategoryById(
                                                            parentCategoryId,
                                                            categoryController
                                                                .categoryList);

                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (_) =>
                                                                BrandAndCategoryProductScreen(
                                                                  isBrand:
                                                                      false,
                                                                  id: subCategoryId,
                                                                  name:
                                                                      parentCategoryName,
                                                                  categoryModel:
                                                                      categoryModel,
                                                                )));
                                                  },
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 4.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        ClipRRect(
                                                          borderRadius: BorderRadius
                                                              .circular(Dimensions
                                                                  .paddingSizeExtraSmall),
                                                          child: Container(
                                                            width: ResponsiveHelper
                                                                    .isTab(
                                                                        context)
                                                                ? 60
                                                                : 55,
                                                            height: ResponsiveHelper
                                                                    .isTab(
                                                                        context)
                                                                ? 60
                                                                : 55,
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                      Dimensions
                                                                          .paddingSizeExtraSmall),
                                                              border: Border.all(
                                                                  width: 0.50,
                                                                  color: Theme.of(
                                                                          context)
                                                                      .primaryColor
                                                                      .withValues(
                                                                          alpha:
                                                                              0.125)),
                                                            ),
                                                            child:
                                                                CustomImageWidget(
                                                              fit: BoxFit.cover,
                                                              image:
                                                                  '${productController.findWhatYouNeedModel!.findWhatYouNeed![index].childes?[subIndex].iconFullUrl?.path}',
                                                            ),
                                                          ),
                                                        ),
                                                        // Removed SizedBox spacing since text is hidden
                                                        // Hidden: Sub-category name text
                                                        // SizedBox(
                                                        //   width: MediaQuery.of(
                                                        //               context)
                                                        //           .size
                                                        //           .width /
                                                        //       7,
                                                        //   child: Text(
                                                        //     '${productController.findWhatYouNeedModel!.findWhatYouNeed![index].childes?[subIndex].name}',
                                                        //     maxLines: 1,
                                                        //     overflow: TextOverflow
                                                        //         .ellipsis,
                                                        //     style: textRegular.copyWith(
                                                        //         fontSize: Dimensions
                                                        //             .fontSizeSmall,
                                                        //         color: Theme.of(
                                                        //                 context)
                                                        //             .textTheme
                                                        //             .bodyLarge
                                                        //             ?.color),
                                                        //   ),
                                                        // ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          )
                                        ],
                                      ),
                                    ],
                                  ),
                                ),

                                Positioned(
                                    right: 10,
                                    top: 80,
                                    child: Icon(Icons.arrow_forward_outlined,
                                        color: Theme.of(context).primaryColor,
                                        size: 26)),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                )
              : const SizedBox()
          : const FindWhatYouNeedShimmer();
    });
  }
}
