                        -H/Users/<USER>/Documents/development/all/latest/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/build/app/intermediates/cxx/Debug/244v5z34/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/build/app/intermediates/cxx/Debug/244v5z34/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/android/app/.cxx/Debug/244v5z34/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2