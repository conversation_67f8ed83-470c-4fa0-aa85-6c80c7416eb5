import 'package:flutter/material.dart';
import 'package:flutter_sixvalley_ecommerce/theme/controllers/theme_controller.dart';
import 'package:provider/provider.dart';

// Updated typography for grocery delivery app patterns
const titilliumRegular = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 14, // Increased from 12
);

const titleRegular = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontWeight: FontWeight.w500,
  fontSize: 16, // Increased from 14
);

const titleHeader = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontWeight: FontWeight.w600,
  fontSize: 18, // Reduced from 20 for better mobile balance
);

const titilliumSemiBold = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 14, // Increased from 12
  fontWeight: FontWeight.w600,
);

const titilliumBold = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 16, // Increased from 14
  fontWeight: FontWeight.w700,
);

const titilliumItalic = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 16, // Increased from 14
  fontStyle: FontStyle.italic,
);

const textRegular = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontWeight: FontWeight.w400, // Changed from w300 for better readability
  fontSize: 16, // Increased from 14
);

const textMedium = TextStyle(
    fontFamily: 'SF-Pro-Rounded-Regular',
    fontSize: 16, // Increased from 14
    fontWeight: FontWeight.w500);

const textBold = TextStyle(
    fontFamily: 'SF-Pro-Rounded-Regular',
    fontSize: 16, // Increased from 14
    fontWeight: FontWeight.w600);

const robotoBold = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 16, // Reduced from 18 for better balance
  fontWeight: FontWeight.w700,
);

// New styles for grocery app patterns - reduced for better mobile balance
const productNameStyle = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 16, // Reduced from 18 for better balance
  fontWeight: FontWeight.w600,
);

const productPriceStyle = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 18, // Reduced from 20px for better visual balance
  fontWeight: FontWeight.w700,
);

const sectionHeaderStyle = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 18, // Reduced from 22 for better mobile viewing
  fontWeight: FontWeight.w700,
);

const categoryNameStyle = TextStyle(
  fontFamily: 'SF-Pro-Rounded-Regular',
  fontSize: 14, // Reduced from 16 for better proportion
  fontWeight: FontWeight.w500,
);

class ThemeShadow {
  static List<BoxShadow> getShadow(BuildContext context) {
    List<BoxShadow> boxShadow = [
      BoxShadow(
          color: Provider.of<ThemeController>(context, listen: false).darkTheme
              ? Colors.black26
              : Theme.of(context).primaryColor.withValues(alpha: .075),
          blurRadius: 5,
          spreadRadius: 1,
          offset: const Offset(1, 1))
    ];
    return boxShadow;
  }
}
