import 'package:country_code_picker/country_code_picker.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sixvalley_ecommerce/features/address/controllers/address_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/auth/domain/models/register_model.dart';
import 'package:flutter_sixvalley_ecommerce/features/auth/screens/login_screen.dart';
import 'package:flutter_sixvalley_ecommerce/features/auth/widgets/condition_check_box_widget.dart';
import 'package:flutter_sixvalley_ecommerce/features/auth/widgets/code_picker_widget.dart';
import 'package:flutter_sixvalley_ecommerce/features/location/controllers/location_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/location/screens/select_location_screen.dart';
import 'package:flutter_sixvalley_ecommerce/features/profile/controllers/profile_contrroller.dart';
import 'package:flutter_sixvalley_ecommerce/features/splash/domain/models/config_model.dart' as config;
import 'package:flutter_sixvalley_ecommerce/helper/velidate_check.dart';
import 'package:flutter_sixvalley_ecommerce/localization/language_constrants.dart';
import 'package:flutter_sixvalley_ecommerce/main.dart';
import 'package:flutter_sixvalley_ecommerce/features/auth/controllers/auth_controller.dart';
import 'package:flutter_sixvalley_ecommerce/features/splash/controllers/splash_controller.dart';
import 'package:flutter_sixvalley_ecommerce/theme/controllers/theme_controller.dart';
import 'package:flutter_sixvalley_ecommerce/utill/custom_themes.dart';
import 'package:flutter_sixvalley_ecommerce/utill/dimensions.dart';
import 'package:flutter_sixvalley_ecommerce/utill/images.dart';
import 'package:flutter_sixvalley_ecommerce/common/basewidget/custom_button_widget.dart';
import 'package:flutter_sixvalley_ecommerce/common/basewidget/show_custom_snakbar_widget.dart';
import 'package:flutter_sixvalley_ecommerce/common/basewidget/custom_textfield_widget.dart';
import 'package:flutter_sixvalley_ecommerce/common/basewidget/success_dialog_widget.dart';
import 'package:flutter_sixvalley_ecommerce/features/dashboard/screens/dashboard_screen.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';

class MultiStepSignUpWidget extends StatefulWidget {
  const MultiStepSignUpWidget({super.key});

  @override
  MultiStepSignUpWidgetState createState() => MultiStepSignUpWidgetState();
}

class MultiStepSignUpWidgetState extends State<MultiStepSignUpWidget> {
  // Controllers for Step 1
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _shopNameController = TextEditingController();
  final TextEditingController _gstinController = TextEditingController();

  // Controllers for Step 2
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _zipController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final TextEditingController _latitudeController = TextEditingController();
  final TextEditingController _longitudeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _referController = TextEditingController();

  // Focus nodes for Step 1
  final FocusNode _fNameFocus = FocusNode();
  final FocusNode _lNameFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _shopNameFocus = FocusNode();
  final FocusNode _gstinFocus = FocusNode();

  // Focus nodes for Step 2
  final FocusNode _addressFocus = FocusNode();
  final FocusNode _cityFocus = FocusNode();
  final FocusNode _zipFocus = FocusNode();
  final FocusNode _countryFocus = FocusNode();
  final FocusNode _latitudeFocus = FocusNode();
  final FocusNode _longitudeFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();
  final FocusNode _confirmPasswordFocus = FocusNode();
  final FocusNode _referFocus = FocusNode();

  RegisterModel register = RegisterModel();
  final GlobalKey<FormState> step1FormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> step2FormKey = GlobalKey<FormState>();

  // Step tracking
  int currentStep = 1;

  // Map related variables
  GoogleMapController? _mapController;
  CameraPosition? _cameraPosition;
  bool _updateAddress = false;
  late LatLng _defaultLocation;

  route(bool isRoute, String? token, String? tempToken, String? errorMessage) async {
    var splashController = Provider.of<SplashController>(context, listen: false);
    var authController = Provider.of<AuthController>(context, listen: false);
    var profileController = Provider.of<ProfileController>(context, listen: false);
    String phone = authController.countryDialCode + _phoneController.text.trim();
    
    if (isRoute) {
      if (splashController.configModel!.emailVerification!) {
        authController.sendOtpToEmail(_emailController.text.toString(), tempToken!).then((value) async {
          if (value.response?.statusCode == 200) {
            authController.updateEmail(_emailController.text.toString());
          }
        });
      } else if (splashController.configModel!.phoneVerification!) {
        authController.sendOtpToPhone(phone, tempToken!).then((value) async {
          if (value.isSuccess) {
            authController.updatePhone(phone);
          }
        });
      } else {
        await profileController.getUserInfo(context);
        Navigator.pushAndRemoveUntil(Get.context!, MaterialPageRoute(builder: (_) =>
        const DashBoardScreen()), (route) => false);
        _clearAllControllers();
      }
    } else {
      // Handle specific error messages
      if (errorMessage != null) {
        String displayMessage = errorMessage;
        
        // Check for common error messages and provide user-friendly responses
        if (errorMessage.toLowerCase().contains('email') && 
            (errorMessage.toLowerCase().contains('already') || 
             errorMessage.toLowerCase().contains('exist') ||
             errorMessage.toLowerCase().contains('registered'))) {
          
          // Show dialog for better user experience
          _showEmailExistsDialog();
          return; // Don't show the snackbar since we're showing a dialog
          
        } else if (errorMessage.toLowerCase().contains('phone') && 
                  (errorMessage.toLowerCase().contains('already') || 
                   errorMessage.toLowerCase().contains('exist') ||
                   errorMessage.toLowerCase().contains('registered'))) {
          displayMessage = "${getTranslated('phone_already_exists', context) ?? 'This phone number is already registered'}. ${getTranslated('try_different_phone_or_login', context) ?? 'Please try a different phone number or login instead'}.";
          
          // Go back to step 1 to let user change phone
          setState(() {
            currentStep = 1;
          });
          
          // Focus on phone field
          Future.delayed(const Duration(milliseconds: 300), () {
            _phoneFocus.requestFocus();
          });
        }
        
        showCustomSnackBar(displayMessage, context);
      }
    }
  }

  void _clearAllControllers() {
    _emailController.clear();
    _passwordController.clear();
    _firstNameController.clear();
    _lastNameController.clear();
    _phoneController.clear();
    _confirmPasswordController.clear();
    _referController.clear();
    _shopNameController.clear();
    _gstinController.clear();
    _addressController.clear();
    _cityController.clear();
    _zipController.clear();
    _countryController.clear();
    _latitudeController.clear();
    _longitudeController.clear();
  }

  void _showEmailExistsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(getTranslated('email_already_exists', context) ?? 'Email Already Exists'),
          content: Text(getTranslated('email_already_registered_message', context) ?? 
            'This email address is already registered. Would you like to login instead or use a different email?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Go back to step 1 to change email
                setState(() {
                  currentStep = 1;
                });
                Future.delayed(const Duration(milliseconds: 300), () {
                  _emailFocus.requestFocus();
                });
              },
              child: Text(getTranslated('change_email', context) ?? 'Change Email'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to login screen
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (_) => const LoginScreen())
                );
              },
              child: Text(getTranslated('login_instead', context) ?? 'Login Instead'),
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    
    // Initialize default location
    config.DefaultLocation? dLocation = Provider.of<SplashController>(context, listen: false).configModel?.defaultLocation;
    _defaultLocation = LatLng(
      double.parse(dLocation?.lat ?? '23.8103'), 
      double.parse(dLocation?.lng ?? '90.4125')
    );
    
    Provider.of<AuthController>(context, listen: false).setCountryCode(
      CountryCode.fromCountryCode(Provider.of<SplashController>(context, listen: false).configModel!.countryCode!).dialCode!, 
      notify: false
    );
    
    // Initialize country controller with default country
    _countryController.text = CountryCode.fromCountryCode(Provider.of<SplashController>(context, listen: false).configModel!.countryCode!).name ?? 'Bangladesh';
    
    // Initialize address controller data
    Provider.of<AddressController>(context, listen: false).getRestrictedDeliveryCountryList();
    
    // Initialize location controller and get current location
    _checkPermission(() => Provider.of<LocationController>(context, listen: false).getCurrentLocation(context, true, mapController: _mapController), context);
  }

  void _nextStep() {
    if (currentStep == 1) {
      if (step1FormKey.currentState?.validate() ?? false) {
        setState(() {
          currentStep = 2;
        });
      }
    }
  }

  void _previousStep() {
    if (currentStep == 2) {
      setState(() {
        currentStep = 1;
      });
    }
  }

  void _submitRegistration() {
    final config = Provider.of<SplashController>(context, listen: false).configModel;
    final authProvider = Provider.of<AuthController>(context, listen: false);
    
    if (step2FormKey.currentState?.validate() ?? false) {
      String firstName = _firstNameController.text.trim();
      String lastName = _lastNameController.text.trim();
      String email = _emailController.text.trim();
      String phoneNumber = authProvider.countryDialCode + _phoneController.text.trim();
      String shopName = _shopNameController.text.trim();
      String gstin = _gstinController.text.trim();
      String address = _addressController.text.trim();
      String city = _cityController.text.trim();
      String zip = _zipController.text.trim();
      String country = _countryController.text.trim();
      String latitude = _latitudeController.text.trim();
      String longitude = _longitudeController.text.trim();
      String password = _passwordController.text.trim();

      // Validate email format one more time before submission
      if (!email.contains('@') || !email.contains('.')) {
        showCustomSnackBar('Please enter a valid email address', context);
        setState(() {
          currentStep = 1;
        });
        _emailFocus.requestFocus();
        return;
      }

      // Validate phone number
      if (phoneNumber.length < 10) {
        showCustomSnackBar('Please enter a valid phone number', context);
        setState(() {
          currentStep = 1;
        });
        _phoneFocus.requestFocus();
        return;
      }

      // Validate country
      if (country.isEmpty) {
        showCustomSnackBar('Please select a country', context);
        return;
      }

      register.fName = firstName;
      register.lName = lastName;
      register.email = email;
      register.phone = phoneNumber;
      register.shopName = shopName;
      register.gstin = gstin;
      register.address = address;
      register.city = city;
      register.zip = zip;
      register.country = country;
      register.latitude = latitude.isNotEmpty ? latitude : null;
      register.longitude = longitude.isNotEmpty ? longitude : null;
      register.password = password;
      register.referCode = _referController.text.trim();
      
      // Show loading message
      print('Attempting registration for email: $email');
      
      authProvider.registration(register, route, config!);
    } else {
      showCustomSnackBar('Please fill all required fields correctly', context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Consumer<AuthController>(
        builder: (context, authProvider, _) {
          return Consumer<SplashController>(
            builder: (context, splashProvider, _) {
              return currentStep == 1 ? _buildStep1(authProvider, splashProvider) : _buildStep2(authProvider, splashProvider);
            }
          );
        }
      ),
    ]);
  }

  Widget _buildStep1(AuthController authProvider, SplashController splashProvider) {
    return Form(
      key: step1FormKey,
      child: Column(children: [
        // Step indicator
        Container(
          margin: const EdgeInsets.all(Dimensions.paddingSizeDefault),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        Text(
          '${getTranslated('step', context) ?? 'Step'} 1 ${getTranslated('of', context) ?? 'of'} 2',
          style: textMedium.copyWith(
            fontSize: Dimensions.fontSizeDefault,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: Dimensions.paddingSizeSmall),
        
        Text(
          getTranslated('basic_information', context) ?? 'Basic Information',
          style: textBold.copyWith(
            fontSize: Dimensions.fontSizeLarge,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: Dimensions.paddingSizeDefault),

        // First Name
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault),
          child: CustomTextFieldWidget(
            hintText: getTranslated('first_name', context),
            labelText: getTranslated('first_name', context),
            inputType: TextInputType.name,
            required: true,
            focusNode: _fNameFocus,
            nextFocus: _lNameFocus,
            prefixIcon: Images.username,
            capitalization: TextCapitalization.words,
            controller: _firstNameController,
            validator: (value) => ValidateCheck.validateEmptyText(value, "first_name_field_is_required")
          )
        ),

        // Last Name
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('last_name', context),
            labelText: getTranslated('last_name', context),
            focusNode: _lNameFocus,
            prefixIcon: Images.username,
            nextFocus: _emailFocus,
            required: true,
            capitalization: TextCapitalization.words,
            controller: _lastNameController,
            validator: (value) => ValidateCheck.validateEmptyText(value, "last_name_field_is_required")
          )
        ),

        // Email
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('enter_your_email', context),
            labelText: getTranslated('enter_your_email', context),
            focusNode: _emailFocus,
            nextFocus: _phoneFocus,
            required: true,
            inputType: TextInputType.emailAddress,
            controller: _emailController,
            prefixIcon: Images.email,
            validator: (value) => ValidateCheck.validateEmail(value)
          )
        ),

        // Phone
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('enter_mobile_number', context),
            labelText: getTranslated('enter_mobile_number', context),
            controller: _phoneController,
            focusNode: _phoneFocus,
            nextFocus: _shopNameFocus,
            required: true,
            showCodePicker: true,
            countryDialCode: authProvider.countryDialCode,
            onCountryChanged: (CountryCode countryCode) {
              _phoneFocus.requestFocus();
              authProvider.countryDialCode = countryCode.dialCode!;
              authProvider.setCountryCode(countryCode.dialCode!);
            },
            isAmount: true,
            validator: (value) => ValidateCheck.validateEmptyText(value, "phone_must_be_required"),
            inputAction: TextInputAction.next,
            inputType: TextInputType.phone
          )
        ),

        // Shop Name
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('enter_shop_name', context),
            labelText: getTranslated('shop_name', context),
            focusNode: _shopNameFocus,
            nextFocus: _gstinFocus,
            required: true,
            inputType: TextInputType.text,
            controller: _shopNameController,
            prefixIcon: Images.homeImage,
            capitalization: TextCapitalization.words,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validateEmptyText(value, "shop_name_is_required")
          )
        ),

        // GSTIN (Optional)
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('enter_gstin', context),
            labelText: '${getTranslated('gstin', context)} (${getTranslated('optional', context) ?? 'Optional'})',
            focusNode: _gstinFocus,
            required: false,
            inputType: TextInputType.text,
            controller: _gstinController,
            prefixIcon: Images.address,
            inputAction: TextInputAction.done
          )
        ),

        const SizedBox(height: Dimensions.paddingSizeDefault),

        // Next Button
        Container(
          margin: const EdgeInsets.all(Dimensions.paddingSizeDefault),
          child: CustomButton(
            buttonText: getTranslated('next', context) ?? 'Next',
            onTap: _nextStep,
          ),
        ),

        // Login link
        Center(child: InkWell(
          onTap: () => Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const LoginScreen())),
          child: Padding(
            padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
            child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text(getTranslated('already_have_account', context)!,
                style: titilliumRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).hintColor)),
              const SizedBox(width: Dimensions.paddingSizeSmall),
              Text(getTranslated('login', context)!, style: titilliumSemiBold.copyWith(
                fontSize: Dimensions.fontSizeSmall,
                color: Theme.of(context).primaryColor))
            ])
          )
        )),
      ]),
    );
  }

  Widget _buildStep2(AuthController authProvider, SplashController splashProvider) {
    return Form(
      key: step2FormKey,
      child: Column(children: [
        // Step indicator
        Container(
          margin: const EdgeInsets.all(Dimensions.paddingSizeDefault),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        Text(
          '${getTranslated('step', context) ?? 'Step'} 2 ${getTranslated('of', context) ?? 'of'} 2',
          style: textMedium.copyWith(
            fontSize: Dimensions.fontSizeDefault,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: Dimensions.paddingSizeSmall),
        
        Text(
          getTranslated('address_and_security', context) ?? 'Address & Security',
          style: textBold.copyWith(
            fontSize: Dimensions.fontSizeLarge,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: Dimensions.paddingSizeDefault),

        // Address
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault),
          child: CustomTextFieldWidget(
            hintText: getTranslated('ENTER_YOUR_ADDRESS', context),
            labelText: getTranslated('ENTER_YOUR_ADDRESS', context),
            focusNode: _addressFocus,
            nextFocus: _cityFocus,
            required: true,
            inputType: TextInputType.streetAddress,
            controller: _addressController,
            prefixIcon: Images.address,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validateEmptyText(value, "ADDRESS_FIELD_MUST_BE_REQUIRED")
          )
        ),

        // City
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('ENTER_YOUR_CITY', context),
            labelText: getTranslated('city', context),
            focusNode: _cityFocus,
            nextFocus: _zipFocus,
            required: true,
            inputType: TextInputType.text,
            controller: _cityController,
            prefixIcon: Images.city,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validateEmptyText(value, "CITY_FIELD_MUST_BE_REQUIRED")
          )
        ),

        // Zip Code
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('ENTER_YOUR_ZIP_CODE', context),
            labelText: getTranslated('zip', context),
            focusNode: _zipFocus,
            nextFocus: _countryFocus,
            required: true,
            inputType: TextInputType.text,
            controller: _zipController,
            prefixIcon: Images.city,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validateEmptyText(value, "ZIPCODE_FIELD_MUST_BE_REQUIRED")
          )
        ),

        // Country
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(getTranslated('country', context)!, style: textRegular.copyWith(
                color: Theme.of(context).hintColor,
                fontSize: Dimensions.fontSizeSmall,
              )),
              const SizedBox(height: Dimensions.paddingSizeExtraSmall),
              
              SizedBox(height: 60,
                child: Consumer<AddressController>(
                  builder: (context, addressController, _) {
                    // Auto-select first country if restricted countries are available
                    if (Provider.of<SplashController>(context, listen: false).configModel!.deliveryCountryRestriction == 1 && 
                        addressController.restrictedCountryList.isNotEmpty) {
                      _countryController.text = addressController.restrictedCountryList[0];
                    }
                    
                    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      
                      Provider.of<SplashController>(context, listen: false).configModel!.deliveryCountryRestriction == 1?
                      
                      Container(width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(color: Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(width: .1, color: Theme.of(context).hintColor.withValues(alpha:0.1))),
                        child: DropdownButtonFormField2<String>(
                          isExpanded: true,
                          isDense: true,
                          decoration: InputDecoration(contentPadding: const EdgeInsets.symmetric(vertical: 0),
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(5))),
                          hint: Row(children: [
                            Image.asset(Images.country),
                            const SizedBox(width: Dimensions.paddingSizeSmall),
                            Text(_countryController.text, style: textRegular.copyWith(fontSize: Dimensions.fontSizeDefault, color: Theme.of(context).textTheme.bodyLarge!.color)),
                          ]),
                          items: addressController.restrictedCountryList.map((item) => DropdownMenuItem<String>(
                              value: item, child: Text(item, style: textRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).textTheme.bodyLarge?.color)))).toList(),
                          onChanged: (value) {
                            _countryController.text = value!;
                          },
                          buttonStyleData: const ButtonStyleData(padding: EdgeInsets.only(right: 8),),
                          iconStyleData: IconStyleData(
                              icon: Icon(Icons.arrow_drop_down, color: Theme.of(context).hintColor), iconSize: 24),
                          dropdownStyleData: DropdownStyleData(
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(5),),),
                          menuItemStyleData: const MenuItemStyleData(padding: EdgeInsets.symmetric(horizontal: 16)),
                        ),
                      ):
                      
                      Container(width: MediaQuery.of(context).size.width,
                        padding: const EdgeInsets.symmetric(vertical: Dimensions.paddingSizeSmall),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(Dimensions.paddingSizeSmall),
                            color: Theme.of(context).cardColor,
                            border: Border.all(color: Theme.of(context).hintColor.withValues(alpha:.5))),
                        child: CodePickerWidget(
                          fromCountryList: true,
                          padding: const EdgeInsets.only(left: Dimensions.paddingSizeSmall),
                          flagWidth: 25,
                          onChanged: (val){
                            _countryController.text = val.name!;
                          },
                          initialSelection: _countryController.text,
                          showDropDownButton: true,
                          showCountryOnly: false,
                          showOnlyCountryWhenClosed: true,
                          showFlagDialog: true,
                          hideMainText: false,
                          showFlagMain: false,
                          dialogBackgroundColor: Theme.of(context).cardColor,
                          barrierColor: Provider.of<ThemeController>(context).darkTheme ? Colors.black.withValues(alpha:0.4) : null,
                          textStyle: textRegular.copyWith(
                            fontSize: Dimensions.fontSizeLarge,
                            color: Theme.of(context).textTheme.bodyLarge!.color,
                          ),
                          dialogTextStyle: textRegular.copyWith(
                            fontSize: Dimensions.fontSizeDefault,
                            color: Theme.of(context).textTheme.bodyLarge!.color,
                          ),
                        ),
                      ),
                      
                    ]);
                  }
                )
              ),
            ],
          )
        ),

        // Location Section Header
        Container(
          margin: const EdgeInsets.only(top: Dimensions.paddingSizeDefault, bottom: Dimensions.paddingSizeSmall),
          child: Row(
            children: [
              Expanded(child: Divider(color: Theme.of(context).hintColor)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeDefault),
                child: Text(
                  getTranslated('location_info', context) ?? 'Location Information',
                  style: textMedium.copyWith(
                    color: Theme.of(context).hintColor,
                    fontSize: Dimensions.fontSizeDefault,
                  ),
                ),
              ),
              Expanded(child: Divider(color: Theme.of(context).hintColor)),
            ],
          ),
        ),

        // Latitude
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('latitude', context) ?? 'Latitude',
            labelText: getTranslated('latitude', context) ?? 'Latitude',
            focusNode: _latitudeFocus,
            nextFocus: _longitudeFocus,
            required: false,
            inputType: TextInputType.numberWithOptions(decimal: true),
            controller: _latitudeController,
            prefixIcon: Images.address,
            inputAction: TextInputAction.next,
            onChanged: (value) {
              // Update map position when latitude changes
              if (value.isNotEmpty && _longitudeController.text.isNotEmpty) {
                try {
                  double lat = double.parse(value);
                  double lng = double.parse(_longitudeController.text);
                  LatLng newPosition = LatLng(lat, lng);
                  _mapController?.animateCamera(CameraUpdate.newLatLng(newPosition));
                } catch (e) {
                  // Handle invalid input
                }
              }
            },
          )
        ),

        // Longitude
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('longitude', context) ?? 'Longitude',
            labelText: getTranslated('longitude', context) ?? 'Longitude',
            focusNode: _longitudeFocus,
            nextFocus: _passwordFocus,
            required: false,
            inputType: TextInputType.numberWithOptions(decimal: true),
            controller: _longitudeController,
            prefixIcon: Images.address,
            inputAction: TextInputAction.next,
            onChanged: (value) {
              // Update map position when longitude changes
              if (value.isNotEmpty && _latitudeController.text.isNotEmpty) {
                try {
                  double lat = double.parse(_latitudeController.text);
                  double lng = double.parse(value);
                  LatLng newPosition = LatLng(lat, lng);
                  _mapController?.animateCamera(CameraUpdate.newLatLng(newPosition));
                } catch (e) {
                  // Handle invalid input
                }
              }
            },
          )
        ),

        // Google Map (if enabled)
        Provider.of<SplashController>(context, listen: false).configModel!.mapApiStatus == 1 ?
          Container(
            margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
            child: Consumer<LocationController>(
              builder: (context, locationController, _) {
                return SizedBox(
                  height: MediaQuery.of(context).size.width/2, 
                  width: MediaQuery.of(context).size.width,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(Dimensions.paddingSizeSmall),
                    child: Stack(
                      clipBehavior: Clip.none, 
                      children: [
                        GoogleMap(
                          mapType: MapType.normal,
                          initialCameraPosition: CameraPosition(
                            target: _latitudeController.text.isNotEmpty && _longitudeController.text.isNotEmpty
                              ? LatLng(
                                  double.tryParse(_latitudeController.text) ?? _defaultLocation.latitude,
                                  double.tryParse(_longitudeController.text) ?? _defaultLocation.longitude,
                                )
                              : LatLng(
                                  locationController.position.latitude != 0.0 ? locationController.position.latitude : _defaultLocation.latitude,
                                  locationController.position.longitude != 0.0 ? locationController.position.longitude : _defaultLocation.longitude,
                                ),
                            zoom: 16
                          ),
                          onTap: (latLng) {
                            // Navigate to fullscreen map like in address screen
                            Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) => SelectLocationScreen(
                                googleMapController: _mapController
                              )
                            )).then((value) {
                              // Update coordinates when returning from fullscreen map
                              setState(() {
                                _latitudeController.text = locationController.position.latitude.toString();
                                _longitudeController.text = locationController.position.longitude.toString();
                              });
                            });
                          },
                          zoomControlsEnabled: false,
                          compassEnabled: false,
                          indoorViewEnabled: true,
                          mapToolbarEnabled: false,
                          onCameraIdle: () {
                            if(_updateAddress && _cameraPosition != null) {
                              setState(() {
                                _latitudeController.text = _cameraPosition!.target.latitude.toString();
                                _longitudeController.text = _cameraPosition!.target.longitude.toString();
                              });
                              locationController.updateMapPosition(_cameraPosition, true, null, context);
                            } else {
                              _updateAddress = true;
                            }
                          },
                          onCameraMove: (position) => _cameraPosition = position,
                          onMapCreated: (GoogleMapController controller) {
                            _mapController = controller;
                            if (!_updateAddress && _mapController != null) {
                              locationController.getCurrentLocation(context, true, mapController: _mapController);
                            }
                          }
                        ),
                        
                        // Loading indicator
                        locationController.loading ? 
                        Center(child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor)
                        )) : const SizedBox(),
                        
                        // Location pin
                        Container(
                          width: MediaQuery.of(context).size.width, 
                          alignment: Alignment.center,
                          height: MediaQuery.of(context).size.height,
                          child: Icon(
                            Icons.location_on, 
                            size: 40, 
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        
                        // Fullscreen button
                        Positioned(
                          top: 10, 
                          right: 0,
                          child: InkWell(
                            onTap: () => Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) => SelectLocationScreen(
                                googleMapController: _mapController
                              )
                            )).then((value) {
                              // Update coordinates when returning from fullscreen map
                              setState(() {
                                _latitudeController.text = locationController.position.latitude.toString();
                                _longitudeController.text = locationController.position.longitude.toString();
                              });
                            }),
                            child: Container(
                              width: 30, 
                              height: 30,
                              margin: const EdgeInsets.only(right: Dimensions.paddingSizeLarge),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(Dimensions.paddingSizeSmall),
                                color: Colors.white,
                              ),
                              child: Icon(Icons.fullscreen, color: Theme.of(context).primaryColor, size: 20)
                            )
                          )
                        ),
                      ]
                    )
                  )
                );
              }
            )
          ) : const SizedBox(),

        // Security Section Header
        Container(
          margin: const EdgeInsets.only(top: Dimensions.paddingSizeDefault, bottom: Dimensions.paddingSizeSmall),
          child: Row(
            children: [
              Expanded(child: Divider(color: Theme.of(context).hintColor)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Dimensions.paddingSizeDefault),
                child: Text(
                  getTranslated('security', context) ?? 'Security',
                  style: textMedium.copyWith(
                    color: Theme.of(context).hintColor,
                    fontSize: Dimensions.fontSizeDefault,
                  ),
                ),
              ),
              Expanded(child: Divider(color: Theme.of(context).hintColor)),
            ],
          ),
        ),

        // Password
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            hintText: getTranslated('minimum_password_length', context),
            labelText: getTranslated('password', context),
            controller: _passwordController,
            focusNode: _passwordFocus,
            isPassword: true,
            required: true,
            nextFocus: _confirmPasswordFocus,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validatePassword(value, "password_must_be_required"),
            prefixIcon: Images.pass
          )
        ),

        // Confirm Password
        Container(
          margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
          child: CustomTextFieldWidget(
            isPassword: true,
            required: true,
            hintText: getTranslated('re_enter_password', context),
            labelText: getTranslated('re_enter_password', context),
            controller: _confirmPasswordController,
            focusNode: _confirmPasswordFocus,
            nextFocus: _referFocus,
            inputAction: TextInputAction.next,
            validator: (value) => ValidateCheck.validateConfirmPassword(value, _passwordController.text.trim()),
            prefixIcon: Images.pass
          )
        ),

        // Referral Code (if enabled)
        if (splashProvider.configModel!.refEarningStatus != null && splashProvider.configModel!.refEarningStatus == "1")
          Container(
            margin: const EdgeInsets.only(left: Dimensions.marginSizeDefault, right: Dimensions.marginSizeDefault, top: Dimensions.marginSizeSmall),
            child: CustomTextFieldWidget(
              hintText: getTranslated('enter_refer_code', context),
              labelText: getTranslated('referral_code', context),
              controller: _referController,
              focusNode: _referFocus,
              prefixIcon: Images.referImage,
              prefixColor: Theme.of(context).primaryColor,
              inputAction: TextInputAction.done
            )
          ),

        const SizedBox(height: Dimensions.paddingSizeDefault),
        const ConditionCheckBox(),

        // Buttons Row
        Container(
          margin: const EdgeInsets.all(Dimensions.paddingSizeDefault),
          child: Row(
            children: [
              Expanded(
                child: CustomButton(
                  buttonText: getTranslated('back', context) ?? 'Back',
                  backgroundColor: Theme.of(context).hintColor,
                  onTap: _previousStep,
                ),
              ),
              const SizedBox(width: Dimensions.paddingSizeDefault),
              Expanded(
                flex: 2,
                child: CustomButton(
                  isLoading: authProvider.isLoading,
                  onTap: authProvider.isAcceptTerms ? _submitRegistration : null,
                  buttonText: getTranslated('sign_up', context),
                ),
              ),
            ],
          ),
        ),

        // Login link
        Center(child: InkWell(
          onTap: () => Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => const LoginScreen())),
          child: Padding(
            padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
            child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text(getTranslated('already_have_account', context)!,
                style: titilliumRegular.copyWith(fontSize: Dimensions.fontSizeSmall, color: Theme.of(context).hintColor)),
              const SizedBox(width: Dimensions.paddingSizeSmall),
              Text(getTranslated('login', context)!, style: titilliumSemiBold.copyWith(
                fontSize: Dimensions.fontSizeSmall,
                color: Theme.of(context).primaryColor))
            ])
          )
        )),
      ]),
    );
  }

  void _checkPermission(Function callback, BuildContext context) async {
    LocationPermission permission = await Geolocator.requestPermission();
    if(permission == LocationPermission.denied || permission == LocationPermission.whileInUse) {
      InkWell(onTap: () async{
        Navigator.pop(context);
        await Geolocator.requestPermission();
        _checkPermission(callback, Get.context!);
        },
          child: AlertDialog(content: SuccessDialog(icon: Icons.location_on_outlined, title: '',
              description: getTranslated('you_denied', Get.context!))));
    }else if(permission == LocationPermission.deniedForever) {
      InkWell(onTap: () async{
        if(context.mounted){}
        Navigator.pop(context);
        await Geolocator.openAppSettings();
        _checkPermission(callback, Get.context!);
        },
          child: AlertDialog(content: SuccessDialog(icon: Icons.location_on_outlined, title: '',
              description: getTranslated('you_denied', Get.context!))));
    }else {
      callback();
    }
  }
}
